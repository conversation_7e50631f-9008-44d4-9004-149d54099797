package com.cosfo.mall.order.model.po;

import com.cosfo.mall.wechat.bean.paymch.DirectQueryResult;
import lombok.Data;


/**
 * <AUTHOR>
 * @date : 2022/12/19 17:39
 */
@Data
public class HuiFuiPaymentReceive extends DirectQueryResult {

    /**
     *业务响应码
     */
    private String resp_code;
    /**
     *业务响应信息
     */
    private String resp_desc;
    /**
     *请求时间
     */
    private String req_date;
    /**
     *请求流水号
     */
    private String req_seq_id;
    /**
     *全局流水号
     */
    private String hf_seq_id;
    /**
     *交易类型
     */
    private String trade_type;
    /**
     *交易类型，异步返回有区别
     */
    private String trans_type;
    /**
     *交易金额
     */
    private String trans_amt;
    /**
     *交易状态
     */
    private String trans_stat;
    /**
     *商户号
     */
    private String huifu_id;
    /**
     *通道返回码
     */
    private String bank_code;
    /**
     *通道返回描述
     */
    private String bank_message;
    /**
     *延时标记
     */
    private String delay_acct_flag;
    /**
     *js支付信息
     */
    private String pay_info;
    /**
     *二维码链接
     */
    private String qr_code;
    /**
     *支付宝返回的响应报文
     */
    private String alipay_response;
    /**
     *微信返回的响应报文
     */
    private String wx_response;
    /**
     *银联返回的响应报文
     */
    private String unionpay_response;
    /**
     *备注
     */
    private String remark;

    /**
     *账户号
     */
    private String acct_id;
    /**
     *结算金额
     */
    private String settlement_amt;
    /**
     *手续费扣款标志
     */
    private Integer fee_flag;
    
    /**
     *手续费金额
     */
    private String fee_amount;
    /**
     *汇付侧交易完成时间
     */
    private String trans_finsh_time;
    /**
     *支付完成时间
     */
    private String end_time;
    /**
     *入账时间
     */
    private String acct_date;
    /**
     *用户账单上的交易订单号
     */
    private String out_trans_id;
    /**
     *用户账单上的商户订单号
     */
    private String party_order_id;
    /**
     *借贷记标识
     */
    private String debit_flag;
    /**
     *是否分账交易
     */
    private String is_div;
    /**
     *分账对象
     */
    private String acct_split_bunch;
    /**
     *是否延时交易
     */
    private String is_delay_acct;
    /**
     *微信用户唯一标识码
     */
    private String wx_user_id;
    /**
     *商户终端定位
     */
    private String mer_dev_location;

    /**
     *	手续费补贴信息
     */
    private String trans_fee_allowance_info;
    /**
     *	营销补贴信息
     */
    private String combinedpay_data;
    /**
     *	补贴部分的手续费
     */
    private String combinedpay_fee_amt;

}
